@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom styles for AgentSalud MVP */
body {
  font-family: system-ui, -apple-system, sans-serif;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f5f9;
}

::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

/* Notification animations */
@keyframes slide-in-up {
  from {
    transform: translateY(100%) translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateY(0) translateX(0);
    opacity: 1;
  }
}

@keyframes slide-out-right {
  from {
    transform: translateY(0) translateX(0);
    opacity: 1;
  }
  to {
    transform: translateY(0) translateX(100%);
    opacity: 0;
  }
}

.animate-slide-in-up {
  animation: slide-in-up 0.3s ease-out;
}

.animate-slide-out-right {
  animation: slide-out-right 0.3s ease-in;
}

/* Focus ring improvements for better accessibility */
.focus-ring {
  @apply focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2;
}

/* Touch target improvements for mobile */
.touch-target {
  min-height: 44px;
  min-width: 44px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .bg-blue-50 {
    background-color: #e0f2fe;
  }

  .bg-green-50 {
    background-color: #e8f5e8;
  }

  .bg-red-50 {
    background-color: #ffeaea;
  }

  .bg-yellow-50 {
    background-color: #fffbeb;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .animate-slide-in-up,
  .animate-slide-out-right,
  .animate-pulse,
  .animate-spin {
    animation: none;
  }

  .transition-all,
  .transition-colors,
  .transition-opacity,
  .transition-transform {
    transition: none;
  }
}
